local sessionId = 0

AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    local identifiers = GetPlayerIdentifiers(source)
    
    deferrals.defer()
    
    Wait(0)
    
    deferrals.update('جاري التحقق من البيانات...')
    
    Wait(1000)
    
    deferrals.update('مرحباً بك في السيرفر!')
    
    Wait(500)
    
    deferrals.done()
end)

AddEventHandler('playerJoining', function(oldId)
    local source = source
    sessionId = sessionId + 1
    
    print(('[SessionManager] Player %s joined with session ID %d'):format(GetPlayerName(source), sessionId))
end)

AddEventHandler('playerDropped', function(reason)
    local source = source
    print(('[SessionManager] Player %s left (Reason: %s)'):format(GetPlayerName(source), reason))
end)
