{"name": "fivem-server-saudi", "version": "1.0.0", "description": "FiveM Server with ESX Framework and Saudi Riyal Currency", "main": "server.cfg", "scripts": {"start": "FXServer.exe +exec server.cfg", "install": "npm install", "setup": "echo Setting up FiveM Server..."}, "keywords": ["fivem", "esx", "roleplay", "saudi", "riyal", "mongodb"], "author": "Augment Agent", "license": "MIT", "dependencies": {"mongodb": "^6.0.0", "mysql2": "^3.6.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/fivem-server-saudi.git"}, "bugs": {"url": "https://github.com/your-username/fivem-server-saudi/issues"}, "homepage": "https://github.com/your-username/fivem-server-saudi#readme", "engines": {"node": ">=16.0.0"}}