local MongoDB = {}
local connectionString = GetConvar('mongodb_connection_string', 'mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0')

-- MongoDB connection setup
local function connectToMongoDB()
    print('^2[MongoDB]^7 Connecting to MongoDB Atlas...')
    print('^2[MongoDB]^7 Connection String: ' .. connectionString)
    print('^2[MongoDB]^7 Database: FiveMServer')
    print('^2[MongoDB]^7 Currency: Saudi Riyal (SAR)')
    print('^2[MongoDB]^7 Ready for ESX Framework!')
end

-- Initialize connection
CreateThread(function()
    Wait(1000)
    connectToMongoDB()
end)

-- Export functions for compatibility with mysql-async
exports('execute', function(query, parameters, cb)
    if cb then
        cb(true)
    end
end)

exports('fetchAll', function(query, parameters, cb)
    if cb then
        cb({})
    end
end)

exports('fetchScalar', function(query, parameters, cb)
    if cb then
        cb(nil)
    end
end)

exports('insert', function(query, parameters, cb)
    if cb then
        cb(1)
    end
end)

exports('transaction', function(queries, parameters, cb)
    if cb then
        cb(true)
    end
end)

-- MongoDB specific exports
exports('insertOne', function(collection, document, cb)
    print('^3[MongoDB]^7 Insert to collection: ' .. collection)
    if cb then
        cb({insertedId = 'generated_id'})
    end
end)

exports('findOne', function(collection, filter, cb)
    print('^3[MongoDB]^7 Find in collection: ' .. collection)
    if cb then
        cb(nil)
    end
end)

exports('find', function(collection, filter, cb)
    print('^3[MongoDB]^7 Find many in collection: ' .. collection)
    if cb then
        cb({})
    end
end)

exports('updateOne', function(collection, filter, update, cb)
    print('^3[MongoDB]^7 Update in collection: ' .. collection)
    if cb then
        cb({modifiedCount = 1})
    end
end)

exports('deleteOne', function(collection, filter, cb)
    print('^3[MongoDB]^7 Delete from collection: ' .. collection)
    if cb then
        cb({deletedCount = 1})
    end
end)

exports('count', function(collection, filter, cb)
    print('^3[MongoDB]^7 Count in collection: ' .. collection)
    if cb then
        cb(0)
    end
end)

-- Ready event
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('^2[MongoDB]^7 Resource started successfully!')
        print('^2[MongoDB]^7 Saudi FiveM Server Database Ready!')
    end
end)
