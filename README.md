# FiveM Server - Saudi Arabia Edition

سيرفر FiveM مع السكربتات الجاهزة المشهورة والعملة السعودية (ريال)

## المميزات

- ✅ ESX Framework الأساسي
- ✅ العملة بالريال السعودي (SAR)
- ✅ قاعدة بيانات MongoDB Atlas
- ✅ السكربتات الجاهزة المشهورة
- ✅ نظام الوظائف الكامل
- ✅ نظام البنوك والمحلات
- ✅ معرض السيارات
- ✅ نظام العقارات
- ✅ وظائف الشرطة والإسعاف والميكانيكي

## السكربتات المتضمنة

### الأساسية
- **es_extended** - الفريم ووك الأساسي
- **esx_menu_default** - القوائم الأساسية
- **esx_menu_dialog** - الحوارات
- **esx_menu_list** - القوائم المتقدمة

### الاحتياجات الأساسية
- **esx_basicneeds** - الجوع والعطش
- **esx_status** - حالة اللاعب

### الوظائف
- **esx_jobs** - نظام الوظائف العام
- **esx_policejob** - وظيفة الشرطة
- **esx_ambulancejob** - وظيفة الإسعاف
- **esx_mechanicjob** - وظيفة الميكانيكي

### الاقتصاد
- **esx_shops** - المحلات
- **esx_vehicleshop** - معرض السيارات
- **esx_banking** - النظام المصرفي
- **esx_atm** - أجهزة الصراف الآلي

### العقارات
- **esx_property** - نظام العقارات
- **esx_realestateagentjob** - وظيفة وكيل عقاري

### أخرى
- **esx_drugs** - نظام المخدرات
- **esx_vehiclelock** - قفل السيارات
- **esx_carwash** - غسيل السيارات
- **esx_inventoryhud** - واجهة المخزون
- **esx_adminmenu** - قائمة الإدارة
- **pma-voice** - نظام الصوت

## متطلبات التشغيل

1. **FiveM Server** - أحدث إصدار
2. **MongoDB Atlas** - قاعدة البيانات (مُعدة مسبقاً)
3. **License Key** - مفتاح ترخيص FiveM

## طريقة التشغيل

### 1. إعداد المفاتيح
قم بتحرير ملف `server.cfg` وغيّر:
```
sv_licensekey "changeme"  # ضع مفتاح الترخيص هنا
set steam_webApiKey "changeme"  # ضع مفتاح Steam API هنا (اختياري)
```

### 2. إعداد الإدارة
في ملف `server.cfg`، غيّر Steam ID الخاص بك:
```
add_principal identifier.steam:110000112345678 group.admin
```

### 3. تشغيل السيرفر
- **Windows**: شغّل `start_server.bat`
- **Linux**: شغّل `./FXServer +exec server.cfg`

## إعدادات قاعدة البيانات

قاعدة البيانات مُعدة مسبقاً للاتصال بـ MongoDB Atlas:
```
mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
```

## العملة والأسعار

جميع الأسعار بالريال السعودي (SAR):

### الرواتب الأساسية
- عاطل: 200 ريال/ساعة
- شرطي: 20-100 ريال/ساعة (حسب الرتبة)
- مسعف: 20-80 ريال/ساعة (حسب الرتبة)
- ميكانيكي: 12-48 ريال/ساعة (حسب الرتبة)

### أسعار المحلات
- خبز: 50 ريال
- ماء: 25 ريال
- بيرة: 150 ريال
- نبيذ: 300 ريال
- أداة فتح الأقفال: 500 ريال
- عدة إصلاح: 750 ريال

### أسعار السيارات (أمثلة)
- سيارات صغيرة: 8,000 - 38,000 ريال
- سيارات رياضية: 95,000 - 2,550,000 ريال
- سيارات خارقة: 150,000 - 4,000,000 ريال

## الوظائف المتاحة

### وظائف عامة (بدون قائمة بيضاء)
- عاطل
- تاكسي
- حطاب
- صياد
- عامل وقود
- مراسل
- سائق شاحنة
- عامل منجم

### وظائف خاصة (تحتاج قائمة بيضاء)
- شرطة
- إسعاف
- ميكانيكي
- وكيل عقاري
- بائع سيارات

## الأوامر الإدارية

```
/car [model] - استدعاء سيارة
/tp - الانتقال للعلامة
/goto [id] - الذهاب للاعب
/bring [id] - جلب لاعب
/setjob [id] [job] [grade] - تعيين وظيفة
/givemoney [id] [account] [amount] - إعطاء أموال
/giveitem [id] [item] [amount] - إعطاء عنصر
/revive [id] - إحياء لاعب
/heal [id] - شفاء لاعب
/freeze [id] - تجميد لاعب
/unfreeze [id] - إلغاء تجميد لاعب
```

## المنافذ والاتصال

- **منفذ السيرفر**: 30120
- **الحد الأقصى للاعبين**: 64
- **OneSync**: مفعل

## الدعم والمساعدة

للحصول على المساعدة:
1. تحقق من ملف `server.cfg`
2. تأكد من صحة مفتاح الترخيص
3. تحقق من اتصال قاعدة البيانات
4. راجع ملفات السجل في مجلد `logs/`

## ملاحظات مهمة

- تأكد من تحديث FiveM Server للإصدار الأحدث
- قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
- راجع إعدادات الأمان في `server.cfg`
- تأكد من إعداد الجدار الناري للسماح بالمنفذ 30120

## الترخيص

هذا السيرفر يستخدم ESX Framework وسكربتات مفتوحة المصدر.
تأكد من احترام تراخيص جميع السكربتات المستخدمة.

---

**تم إنشاؤه بواسطة Augment Agent**
**العملة: ريال سعودي (SAR)**
**قاعدة البيانات: MongoDB Atlas**
