local currentGameType = ''
local currentMap = ''
local gameTypes = {}
local maps = {}

function getCurrentGameType()
    return currentGameType
end

function getCurrentMap()
    return currentMap
end

function changeGameType(gameType, cbObj)
    if not doesGameTypeExist(gameType) then
        cbObj({
            error = 'Game type does not exist'
        })
        return
    end

    currentGameType = gameType
    TriggerClientEvent('mapmanager:gameTypeChanged', -1, gameType)
    
    cbObj({})
end

function changeMap(map, cbObj)
    if not doesMapExist(map) then
        cbObj({
            error = 'Map does not exist'
        })
        return
    end

    currentMap = map
    TriggerClientEvent('mapmanager:mapChanged', -1, map)
    
    cbObj({})
end

function doesGameTypeExist(gameType)
    return gameTypes[gameType] ~= nil
end

function doesMapExist(map)
    return maps[map] ~= nil
end

function getMaps()
    return maps
end

function getGameTypes()
    return gameTypes
end

exports('getCurrentGameType', getCurrentGameType)
exports('getCurrentMap', getCurrentMap)
exports('changeGameType', changeGameType)
exports('changeMap', changeMap)
exports('doesGameTypeExist', doesGameTypeExist)
exports('doesMapExist', doesMapExist)
exports('getMaps', getMaps)
exports('getGameTypes', getGameTypes)

AddEventHandler('onResourceStart', function(resourceName)
    if GetResourceMetadata(resourceName, 'map', 0) then
        maps[resourceName] = {
            name = resourceName,
            author = GetResourceMetadata(resourceName, 'author', 0) or 'Unknown',
            version = GetResourceMetadata(resourceName, 'version', 0) or '1.0.0'
        }
    end
    
    if GetResourceMetadata(resourceName, 'gametype', 0) then
        gameTypes[resourceName] = {
            name = resourceName,
            author = GetResourceMetadata(resourceName, 'author', 0) or 'Unknown',
            version = GetResourceMetadata(resourceName, 'version', 0) or '1.0.0'
        }
    end
end)

AddEventHandler('onResourceStop', function(resourceName)
    if maps[resourceName] then
        maps[resourceName] = nil
    end
    
    if gameTypes[resourceName] then
        gameTypes[resourceName] = nil
    end
end)
