========================================
    FiveM Server - Saudi Arabia Edition
========================================

Server Information:
- Name: FiveM Server - Saudi Arabia
- Currency: Saudi Riyal (SAR)
- Framework: ESX Legacy
- Database: MongoDB Atlas
- Max Players: 64
- OneSync: Enabled

Database Connection:
mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0

Required Setup:
1. Set sv_licensekey in server.cfg
2. Set steam_webApiKey in server.cfg (optional)
3. Update admin Steam ID in server.cfg
4. Download FiveM Server files
5. Run start_server.bat

Included Resources:
✓ ESX Framework
✓ Basic Needs System
✓ Job System (Police, Ambulance, Mechanic)
✓ Vehicle Shop
✓ Banking System
✓ Property System
✓ Drug System
✓ Admin Menu
✓ Voice Chat (pma-voice)

Currency Pricing (SAR):
- Starting Money: 5,000 SAR cash + 50,000 SAR bank
- Basic Items: 25-750 SAR
- Vehicles: 8,000 - 4,000,000 SAR
- Job Salaries: 12-100 SAR per hour

Default Jobs:
- unemployed (عاطل)
- police (شرطة) - Whitelisted
- ambulance (إسعاف) - Whitelisted  
- mechanic (ميكانيكي) - Whitelisted
- taxi (تاكسي)
- lumberjack (حطاب)
- fisherman (صياد)
- fueler (وقود)
- reporter (مراسل)

Admin Commands:
/car [model] - Spawn vehicle
/tp - Teleport to waypoint
/goto [id] - Go to player
/bring [id] - Bring player
/setjob [id] [job] [grade] - Set job
/givemoney [id] [account] [amount] - Give money
/giveitem [id] [item] [amount] - Give item
/revive [id] - Revive player
/heal [id] - Heal player

Support:
- Check server.cfg for configuration
- Verify license key is valid
- Ensure MongoDB connection is working
- Check logs folder for errors

Created by: Augment Agent
Date: 2025-07-09
Version: 1.0.0
