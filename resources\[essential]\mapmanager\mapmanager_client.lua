local currentGameType = ''
local currentMap = ''

RegisterNetEvent('mapmanager:gameTypeChanged')
AddEventHandler('mapmanager:gameTypeChanged', function(gameType)
    currentGameType = gameType
end)

RegisterNetEvent('mapmanager:mapChanged')
AddEventHandler('mapmanager:mapChanged', function(map)
    currentMap = map
end)

function getCurrentGameType()
    return currentGameType
end

function getCurrentMap()
    return currentMap
end

exports('getCurrentGameType', getCurrentGameType)
exports('getCurrentMap', getCurrentMap)
