fx_version 'adamant'

game 'gta5'

description 'ESX Framework'

version '1.9.4'

shared_scripts {
	'@es_extended/imports.lua',
	'@es_extended/locale.lua',
	'locales/*.lua',
	'config.lua'
}

server_scripts {
	'@oxmysql/lib/MySQL.lua',
	'server/common.lua',
	'server/classes/player.lua',
	'server/classes/overrides.lua',
	'server/functions.lua',
	'server/paycheck.lua',
	'server/main.lua',
	'server/commands.lua',
	'common/modules/math.lua',
	'common/modules/table.lua',
	'common/functions.lua'
}

client_scripts {
	'client/common.lua',
	'client/entityiter.lua',
	'client/functions.lua',
	'client/wrapper.lua',
	'client/main.lua',
	'common/modules/math.lua',
	'common/modules/table.lua',
	'common/functions.lua'
}

ui_page {
	'html/ui.html'
}

files {
	'imports.lua',
	'locale.lua',
	'html/ui.html',
	'html/css/app.css',
	'html/js/mustache.min.js',
	'html/js/wrapper.js',
	'html/js/app.js',
	'html/fonts/pdown.woff',
	'html/fonts/bankgothic.woff'
}

dependencies {
	'spawnmanager',
	'oxmysql'
}

provide 'mysql-async'
provide 'essentialmode'

lua54 'yes'
use_experimental_fxv2_oal 'yes'
