-- Simple Spawn Manager for FiveM Server - Saudi Arabia

local spawnPoints = {}
local autoSpawn = false
local autoSpawnCallback = nil

-- Default spawn point (Los Santos)
local defaultSpawn = {
    x = -269.4,
    y = -955.3,
    z = 31.2,
    heading = 205.8
}

-- Add spawn point
function addSpawnPoint(spawn)
    table.insert(spawnPoints, spawn)
end

-- Remove spawn point
function removeSpawnPoint(spawn)
    for i, v in ipairs(spawnPoints) do
        if v.x == spawn.x and v.y == spawn.y and v.z == spawn.z then
            table.remove(spawnPoints, i)
            break
        end
    end
end

-- Load spawns
function loadSpawns()
    return spawnPoints
end

-- Set auto spawn
function setAutoSpawn(enabled)
    autoSpawn = enabled
end

-- Set auto spawn callback
function setAutoSpawnCallback(callback)
    autoSpawnCallback = callback
end

-- Spawn player
function spawnPlayer(playerId, spawn, callback)
    local spawn = spawn or defaultSpawn
    
    if spawn then
        SetEntityCoords(GetPlayerPed(playerId), spawn.x, spawn.y, spawn.z, false, false, false, true)
        if spawn.heading then
            SetEntityHeading(GetPlayerPed(playerId), spawn.heading)
        end
        
        if callback then
            callback()
        end
    end
end

-- Force respawn
function forceRespawn(playerId)
    spawnPlayer(playerId, defaultSpawn)
end

-- Player connecting event
AddEventHandler('playerConnecting', function()
    local source = source
    print('^2[SpawnManager]^7 Player ' .. source .. ' connecting...')
end)

-- Player spawned event
AddEventHandler('playerSpawned', function()
    local source = source
    print('^2[SpawnManager]^7 Player ' .. source .. ' spawned at default location')
    
    if autoSpawn and autoSpawnCallback then
        autoSpawnCallback(source)
    end
end)

-- Exports
exports('spawnPlayer', spawnPlayer)
exports('addSpawnPoint', addSpawnPoint)
exports('removeSpawnPoint', removeSpawnPoint)
exports('loadSpawns', loadSpawns)
exports('setAutoSpawn', setAutoSpawn)
exports('setAutoSpawnCallback', setAutoSpawnCallback)
exports('forceRespawn', forceRespawn)

-- Initialize
CreateThread(function()
    -- Add default spawn point
    addSpawnPoint(defaultSpawn)
    
    print('^2[SpawnManager]^7 Spawn Manager loaded!')
    print('^2[SpawnManager]^7 Default spawn: Los Santos Airport')
    print('^2[SpawnManager]^7 Saudi FiveM Server Ready!')
end)
