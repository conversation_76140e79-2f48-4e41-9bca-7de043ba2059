Config = {}
Config.Locale = 'en'

Config.Accounts = {
	bank = {
		label = _U('account_bank'),
		round = true
	},
	black_money = {
		label = _U('account_black_money'),
		round = true
	},
	money = {
		label = _U('account_money'),
		round = true
	}
}

Config.StartingAccountMoney = {bank = 50000, money = 5000} -- SAR

Config.StartingInventoryItems = false -- table/false

Config.DefaultSpawn = {x = -269.4, y = -955.3, z = 31.2, heading = 205.8}

Config.AdminGroups = {
	['owner'] = true,
	['admin'] = true,
	['moderator'] = true
}

Config.EnablePaycheck = true -- enable paycheck
Config.LogPaycheck = false -- log all paychecks
Config.EnableSocietyPayouts = false -- pay from the society account that the player is employed at? Requirement: esx_society
Config.MaxWeight = 30 -- the max inventory weight without backpack
Config.PaycheckInterval = 7 * 60000 -- how often to receive paychecks in milliseconds
Config.EnableHud = true -- enable the default hud? Display current job and accounts (black, bank & cash)
Config.EnablePVP = false -- Allow Player vs Player combat

Config.Currency = 'SAR' -- Currency symbol
Config.Locale = 'ar' -- Locale for formatting

Config.EnableDebug = false -- Use Debug options?

Config.EnableDefaultInventory = false -- Display the default Inventory ( F2 )
Config.EnableWantedLevel = false -- Use Normal GTA wanted Level?
Config.EnableRPChat = true -- Enable roleplay chat commands
Config.EnablePlayerManagement = true -- Enable society managing

Config.NPCJobEarnings = {min = 150, max = 400} -- SAR

Config.DisableHealthRegeneration = true -- Player will no longer regenerate health
Config.DisableVehicleRewards = true -- Disables Player Recieving weapons from vehicles
Config.DisableNPCDrops = true -- stops NPCs from dropping weapons on death
Config.DisableDispatchServices = true -- Disable Dispatch services
Config.DisableScenarios = true -- Disable Scenarios
Config.DisableWeaponWheel = true -- Disables default weapon wheel
Config.DisableAimAssist = true -- disables AIM assist (mainly on controllers)
Config.DisableVehicleSeatShuff = true -- Disables vehicle seat shuff

Config.RemoveHudComponents = {
	[1] = false, --WANTED_STARS,
	[2] = false, --WEAPON_ICON
	[3] = false, --CASH
	[4] = false, --MP_CASH
	[5] = false, --MP_MESSAGE
	[6] = false, --VEHICLE_NAME
	[7] = true, -- AREA_NAME
	[8] = true, -- VEHICLE_CLASS
	[9] = true, -- STREET_NAME
	[10] = false, --HELP_TEXT
	[11] = false, --FLOATING_HELP_TEXT_1
	[12] = false, --FLOATING_HELP_TEXT_2
	[13] = false, --CASH_CHANGE
	[14] = false, --RETICLE
	[15] = false, --SUBTITLE_TEXT
	[16] = false, --RADIO_STATIONS
	[17] = false, --SAVING_GAME,
	[18] = false, --GAME_STREAM
	[19] = false, --WEAPON_WHEEL
	[20] = false, --WEAPON_WHEEL_STATS
	[21] = false, --HUD_COMPONENTS
	[22] = false, --HUD_WEAPONS
}

Config.SpawnVehMaxUpgrades = true -- admin vehicles spawn with max vehicle settings
Config.CustomAIPlates = 'ESX 111' -- Custom plates for AI vehicles 
-- Pattern string format
--1 will lead to a random number from 0-9.
--A will lead to a random letter from A-Z.
-- . will lead to a random letter or number, with a 50% probability of being either.
--^1 will lead to a literal 1 being emitted.
--^A will lead to a literal A being emitted.
--Any other character will lead to said character being emitted.
-- A string shorter than 8 characters will be padded on the right.
