local showPlayerNames = true

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        if showPlayerNames then
            local players = GetActivePlayers()
            
            for _, playerId in ipairs(players) do
                local playerPed = GetPlayerPed(playerId)
                local playerCoords = GetEntityCoords(playerPed)
                local myCoords = GetEntityCoords(PlayerPedId())
                local distance = #(playerCoords - myCoords)
                
                if distance < 50.0 and playerId ~= PlayerId() then
                    local playerName = GetPlayerName(playerId)
                    local x, y, z = table.unpack(playerCoords)
                    
                    DrawText3D(x, y, z + 1.0, playerName)
                end
            end
        end
    end
end)

function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    
    if onScreen then
        SetTextScale(0.35, 0.35)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
        
        local factor = (string.len(text)) / 370
        DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 0, 0, 0, 68)
    end
end
