RegisterCommand('say', function(source, args, rawCommand)
    local message = table.concat(args, ' ')
    if message and #message > 0 then
        TriggerClientEvent('chat:addMessage', -1, {
            args = {GetPlayerName(source), message}
        })
    end
end, false)

RegisterCommand('me', function(source, args, rawCommand)
    local message = table.concat(args, ' ')
    if message and #message > 0 then
        TriggerClientEvent('chat:addMessage', -1, {
            color = {255, 194, 14},
            args = {'* ' .. GetPlayerName(source) .. ' ' .. message}
        })
    end
end, false)

RegisterCommand('ooc', function(source, args, rawCommand)
    local message = table.concat(args, ' ')
    if message and #message > 0 then
        TriggerClientEvent('chat:addMessage', -1, {
            color = {128, 128, 128},
            args = {'[OOC] ' .. GetPlayerName(source), message}
        })
    end
end, false)

AddEventHandler('chatMessage', function(source, name, message)
    if string.sub(message, 1, 1) == '/' then
        CancelEvent()
        return
    end
    
    TriggerClientEvent('chat:addMessage', -1, {
        args = {name, message}
    })
end)
