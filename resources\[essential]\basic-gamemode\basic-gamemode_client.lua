local spawnPos = vector4(-1035.71, -2731.87, 12.86, 0.0)

AddEventHandler('onClientGameTypeStart', function()
    exports.spawnmanager:setAutoSpawnCallback(function()
        exports.spawnmanager:spawnPlayer({
            x = spawnPos.x,
            y = spawnPos.y,
            z = spawnPos.z,
            heading = spawnPos.w,
            model = 'mp_m_freemode_01',
            skipFade = false
        }, function()
            TriggerEvent('chat:addMessage', {
                color = { 0, 255, 0},
                multiline = true,
                args = {"السيرفر", "مرحباً بك في السيرفر السعودي!"}
            })
        end)
    end)

    exports.spawnmanager:setAutoSpawn(true)
    exports.spawnmanager:forceRespawn()
end)

AddEventHandler('onClientGameTypeStop', function()
    exports.spawnmanager:setAutoSpawn(false)
end)
