local spawnPos = {x = -1035.71, y = -2731.87, z = 12.86, heading = 0.0}

-- Auto spawn when player joins
AddEventHandler('playerSpawned', function()
    print('[Basic Gamemode] Player spawned')
end)

-- Set spawn point
AddEventHandler('onClientGameTypeStart', function()
    print('[Basic Gamemode] Game type started')

    exports.spawnmanager:setAutoSpawnCallback(function()
        print('[Basic Gamemode] Auto spawn callback triggered')

        exports.spawnmanager:spawnPlayer({
            x = spawnPos.x,
            y = spawnPos.y,
            z = spawnPos.z,
            heading = spawnPos.heading,
            model = 'mp_m_freemode_01',
            skipFade = false
        }, function()
            print('[Basic Gamemode] Player spawned successfully')

            -- Send welcome message
            TriggerEvent('chat:addMessage', {
                color = {0, 255, 0},
                multiline = true,
                args = {"السيرفر", "مرحباً بك في السيرفر السعودي!"}
            })
        end)
    end)

    exports.spawnmanager:setAutoSpawn(true)
    exports.spawnmanager:forceRespawn()
end)

-- Force spawn on resource start
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)

        if NetworkIsSessionStarted() then
            TriggerEvent('onClientGameTypeStart')
            break
        end
    end
end)

AddEventHandler('onClientGameTypeStop', function()
    exports.spawnmanager:setAutoSpawn(false)
end)
