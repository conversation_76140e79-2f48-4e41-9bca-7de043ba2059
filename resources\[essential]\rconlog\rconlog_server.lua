-- RCON logging functionality
AddEventHandler('rconCommand', function(commandName, args)
    local source = source
    print(('[RCON] Command executed: %s %s'):format(commandName, table.concat(args, ' ')))
end)

-- Log server events
AddEventHandler('onResourceStart', function(resourceName)
    print(('[RCON] Resource started: %s'):format(resourceName))
end)

AddEventHandler('onResourceStop', function(resourceName)
    print(('[RCON] Resource stopped: %s'):format(resourceName))
end)
