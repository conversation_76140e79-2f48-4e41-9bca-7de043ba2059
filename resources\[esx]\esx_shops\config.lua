Config = {}

Config.Locale = 'ar'

Config.Markers = {
	Shop = {
		Type = 1, Size = {x = 1.5, y = 1.5, z = 0.5}, Color = {r = 0, g = 155, b = 255}, DrawDistance = 15.0
	},
	ShopItems = {
		Type = 1, Size = {x = 1.5, y = 1.5, z = 0.5}, Color = {r = 0, g = 155, b = 255}, DrawDistance = 15.0
	}
}

Config.Shops = {
	TwentyFourSeven = {
		Blip = {
			Coords = vector3(25.7, -1347.3, 29.49),
			Sprite = 52, Display = 4, Scale = 0.7, Colour = 0, Name = _U('shop_blip')
		},
		Items = {bread = 50, water = 25},
		Locations = {
			vector3(25.7, -1347.3, 29.49),
			vector3(-3038.71, 585.9, 7.9),
			vector3(-3241.927, 1001.462, 12.83),
			vector3(1728.66, 6414.16, 17.46),
			vector3(1697.99, 4924.4, 42.06),
			vector3(1961.48, 3739.96, 32.34),
			vector3(547.79, 2671.79, 42.15),
			vector3(1166.024, 2708.930, 38.157),
			vector3(2679.25, 3280.12, 55.24),
			vector3(2557.94, 382.05, 108.62),
			vector3(373.55, 325.56, 103.56),
			vector3(2555.89, 4681.25, 34.07),
			vector3(6.15, 6511.81, 31.87),
			vector3(-1820.523, 792.518, 138.118),
			vector3(1392.562, 3604.684, 34.980),
			vector3(-2968.243, 390.910, 15.043),
			vector3(-1487.553, -379.107, 40.163),
			vector3(-1222.915, -908.119, 12.326),
			vector3(-707.501, -914.260, 19.215),
			vector3(1163.373, -323.801, 69.205),
			vector3(-47.522, -1757.514, 29.421),
			vector3(1698.388, 4929.394, 42.078)
		}
	},

	RobsLiquor = {
		Blip = {
			Coords = vector3(-1222.915, -908.119, 12.326),
			Sprite = 52, Display = 4, Scale = 0.7, Colour = 0, Name = _U('liquor_blip')
		},
		Items = {bread = 50, water = 25, beer = 150, wine = 300},
		Locations = {
			vector3(-1222.915, -908.119, 12.326),
			vector3(-1487.553, -379.107, 40.163),
			vector3(-2968.243, 390.910, 15.043),
			vector3(1166.024, 2708.930, 38.157),
			vector3(1392.562, 3604.684, 34.980),
			vector3(-1393.409, -606.624, 30.319)
		}
	},

	YouTool = {
		Blip = {
			Coords = vector3(2747.8, 3472.86, 55.67),
			Sprite = 402, Display = 4, Scale = 0.7, Colour = 0, Name = _U('hardware_blip')
		},
		Items = {bread = 50, water = 25, lockpick = 500, flashlight = 200, fixkit = 750, rope = 150},
		Locations = {
			vector3(2747.8, 3472.86, 55.67),
			vector3(342.99, -1298.26, 32.51)
		}
	},

	Ammunation = {
		Blip = {
			Coords = vector3(-662.180, -934.961, 21.829),
			Sprite = 110, Display = 4, Scale = 0.7, Colour = 1, Name = _U('gun_blip')
		},
		Items = {},
		Locations = {
			vector3(-662.180, -934.961, 21.829),
			vector3(810.25, -2157.60, 29.62),
			vector3(1693.44, 3760.16, 34.71),
			vector3(-330.24, 6083.88, 31.45),
			vector3(252.63, -50.00, 69.94),
			vector3(22.56, -1109.89, 29.80),
			vector3(2567.69, 294.38, 108.73),
			vector3(-1117.58, 2698.61, 18.55),
			vector3(842.44, -1033.42, 28.19)
		}
	}
}

Config.DefaultPrice = 100 -- SAR

Config.LicensePrice = 5000 -- SAR

Config.LicenseEnabled = true -- require people to own a weapon license in order to buy weapons? Only applies if esx_license is used

Config.Items = {
	bread = 50,
	water = 25,
	beer = 150,
	wine = 300,
	lockpick = 500,
	flashlight = 200,
	fixkit = 750,
	rope = 150,
	bandage = 100,
	medikit = 500,
	phone = 1000,
	radio = 750,
	cigarette = 25,
	lighter = 50,
	cola = 30,
	burger = 75,
	sandwich = 60,
	donut = 40,
	coffee = 35
}
