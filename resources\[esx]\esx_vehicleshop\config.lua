Config = {}

Config.Locale = 'ar'

Config.LicenseEnable = true -- require people to own a driver license when buying vehicles? Only applies if esx_license is used
Config.LicensePrice = 5000 -- SAR

Config.DrawDistance = 100
Config.MarkerSize   = {x = 3.0, y = 3.0, z = 1.0}
Config.MarkerColor  = {r = 120, g = 120, b = 240}
Config.MarkerType   = 1

Config.EnablePlayerManagement = true -- enables the actual car dealer job. You'll need esx_addonaccount, esx_billing and esx_society
Config.EnableOwnedVehicles = true
Config.EnableSocietyOwnedVehicles = false -- use with EnablePlayerManagement disabled, or else it wont have any effects
Config.ResellPercentage = 75 -- percent of the purchase price players get when reselling a vehicle (between 0 - 100)

Config.Plate_Letters  = 3
Config.Plate_Numbers  = 3
Config.Plate_Use_Map  = false -- Use plate generation from esx_vehicleshop/config.lua

Config.Zones = {

	ShopEntering = {
		Pos   = {x = -56.727, y = -1096.694, z = 25.422},
		Size  = {x = 3.0, y = 3.0, z = 1.0},
		Color = {r = 120, g = 120, b = 240},
		Type  = 1
	},

	ShopInside = {
		Pos     = {x = -47.059, y = -1097.862, z = 25.422},
		Size    = {x = 5.0, y = 5.0, z = 1.0},
		Heading = 69.0
	},

	ShopOutside = {
		Pos     = {x = -31.454, y = -1090.654, z = 25.422},
		Size    = {x = 5.0, y = 5.0, z = 1.0},
		Heading = 158.0
	},

	BuyVehicle = {
		Pos   = {x = -56.727, y = -1096.694, z = 25.422},
		Size  = {x = 3.0, y = 3.0, z = 1.0},
		Color = {r = 120, g = 120, b = 240},
		Type  = 1
	}

}

Config.Categories = {
	['super'] = {
		label = 'سيارات خارقة',
		vehicles = {
			'adder',
			'banshee2',
			'bullet',
			'cheetah',
			'entityxf',
			'fmj',
			'gp1',
			'infernus',
			'osiris',
			'reaper',
			't20',
			'tempesta',
			'turismor',
			'tyrus',
			'vacca',
			'voltic',
			'zentorno'
		}
	},

	['sports'] = {
		label = 'سيارات رياضية',
		vehicles = {
			'alpha',
			'banshee',
			'bestiagts',
			'blista2',
			'blista3',
			'buffalo',
			'buffalo2',
			'buffalo3',
			'carbonizzare',
			'comet2',
			'coquette',
			'elegy',
			'elegy2',
			'feltzer2',
			'furoregt',
			'fusilade',
			'jester',
			'jester2',
			'khamelion',
			'kuruma',
			'lynx',
			'massacro',
			'massacro2',
			'ninef',
			'ninef2',
			'omnis',
			'penumbra',
			'rapidgt',
			'rapidgt2',
			'schafter3',
			'sultan',
			'surano',
			'tropos',
			'verlierer2'
		}
	},

	['sportsclassics'] = {
		label = 'سيارات كلاسيكية رياضية',
		vehicles = {
			'ardent',
			'btype',
			'btype2',
			'btype3',
			'casco',
			'coquette2',
			'jb700',
			'mamba',
			'manana',
			'monroe',
			'peyote',
			'pigalle',
			'stinger',
			'stingergt',
			'torero',
			'tornado',
			'tornado2',
			'tornado3',
			'tornado4',
			'tornado5',
			'tornado6',
			'turismo2',
			'viseris',
			'z190'
		}
	},

	['sedans'] = {
		label = 'سيدان',
		vehicles = {
			'asea',
			'asterope',
			'cog55',
			'cog552',
			'cognoscenti',
			'cognoscenti2',
			'emperor',
			'emperor2',
			'emperor3',
			'fugitive',
			'glendale',
			'ingot',
			'intruder',
			'limo2',
			'premier',
			'primo',
			'primo2',
			'regina',
			'romero',
			'schafter2',
			'schafter5',
			'schafter6',
			'stanier',
			'stratum',
			'stretch',
			'superd',
			'surge',
			'tailgater',
			'warrener',
			'washington'
		}
	},

	['compacts'] = {
		label = 'سيارات صغيرة',
		vehicles = {
			'blista',
			'brioso',
			'dilettante',
			'dilettante2',
			'issi2',
			'panto',
			'prairie',
			'rhapsody'
		}
	},

	['coupes'] = {
		label = 'كوبيه',
		vehicles = {
			'cogcabrio',
			'exemplar',
			'f620',
			'felon',
			'felon2',
			'jackal',
			'oracle',
			'oracle2',
			'sentinel',
			'sentinel2',
			'windsor',
			'windsor2',
			'zion',
			'zion2'
		}
	},

	['suvs'] = {
		label = 'سيارات دفع رباعي',
		vehicles = {
			'baller',
			'baller2',
			'baller3',
			'baller4',
			'baller5',
			'baller6',
			'bjxl',
			'cavalcade',
			'cavalcade2',
			'contender',
			'dubsta',
			'dubsta2',
			'fq2',
			'granger',
			'gresley',
			'habanero',
			'huntley',
			'landstalker',
			'mesa',
			'mesa2',
			'patriot',
			'radi',
			'rocoto',
			'seminole',
			'serrano',
			'xls',
			'xls2'
		}
	},

	['vans'] = {
		label = 'فانات',
		vehicles = {
			'bison',
			'bison2',
			'bison3',
			'bobcatxl',
			'boxville',
			'boxville2',
			'boxville3',
			'boxville4',
			'burrito',
			'burrito2',
			'burrito3',
			'burrito4',
			'burrito5',
			'camper',
			'gburrito',
			'gburrito2',
			'journey',
			'minivan',
			'minivan2',
			'paradise',
			'pony',
			'pony2',
			'rumpo',
			'rumpo2',
			'rumpo3',
			'speedo',
			'speedo2',
			'surfer',
			'surfer2',
			'taco',
			'youga',
			'youga2'
		}
	}
}

Config.Vehicles = {
	-- Super Cars
	['adder'] = 4000000,
	['banshee2'] = 565000,
	['bullet'] = 1550000,
	['cheetah'] = 650000,
	['entityxf'] = 795000,
	['fmj'] = 1750000,
	['gp1'] = 1260000,
	['infernus'] = 440000,
	['osiris'] = 1950000,
	['reaper'] = 1595000,
	['t20'] = 2200000,
	['tempesta'] = 1329000,
	['turismor'] = 500000,
	['tyrus'] = 2550000,
	['vacca'] = 240000,
	['voltic'] = 150000,
	['zentorno'] = 725000,

	-- Sports Cars
	['alpha'] = 150000,
	['banshee'] = 105000,
	['bestiagts'] = 610000,
	['blista2'] = 25000,
	['blista3'] = 25000,
	['buffalo'] = 35000,
	['buffalo2'] = 96000,
	['buffalo3'] = 96000,
	['carbonizzare'] = 195000,
	['comet2'] = 100000,
	['coquette'] = 138000,
	['elegy'] = 95000,
	['elegy2'] = 95000,
	['feltzer2'] = 130000,
	['furoregt'] = 448000,
	['fusilade'] = 36000,
	['jester'] = 240000,
	['jester2'] = 350000,
	['khamelion'] = 100000,
	['kuruma'] = 95000,
	['lynx'] = 1735000,
	['massacro'] = 275000,
	['massacro2'] = 385000,
	['ninef'] = 130000,
	['ninef2'] = 130000,
	['omnis'] = 701000,
	['penumbra'] = 24000,
	['rapidgt'] = 140000,
	['rapidgt2'] = 140000,
	['schafter3'] = 116000,
	['sultan'] = 15000,
	['surano'] = 110000,
	['tropos'] = 816000,
	['verlierer2'] = 695000,

	-- Sedans
	['asea'] = 12000,
	['asterope'] = 18000,
	['cog55'] = 138000,
	['cog552'] = 185000,
	['cognoscenti'] = 55000,
	['cognoscenti2'] = 55000,
	['emperor'] = 8500,
	['emperor2'] = 8500,
	['emperor3'] = 8500,
	['fugitive'] = 24000,
	['glendale'] = 15000,
	['ingot'] = 9000,
	['intruder'] = 16000,
	['limo2'] = 75000,
	['premier'] = 10000,
	['primo'] = 9000,
	['primo2'] = 14000,
	['regina'] = 8000,
	['romero'] = 45000,
	['schafter2'] = 65000,
	['schafter5'] = 65000,
	['schafter6'] = 65000,
	['stanier'] = 10000,
	['stratum'] = 10000,
	['stretch'] = 90000,
	['superd'] = 130000,
	['surge'] = 38000,
	['tailgater'] = 55000,
	['warrener'] = 12000,
	['washington'] = 15000
}
