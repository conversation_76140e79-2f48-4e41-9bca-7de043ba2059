local chatInputActive = false
local chatInputActivating = false

RegisterNetEvent('chat:addMessage')
AddEventHandler('chat:addMessage', function(message)
    local args = message.args or {}
    local color = message.color or {255, 255, 255}
    
    -- Simple chat display in console for now
    print(('[CHAT] %s: %s'):format(args[1] or 'Unknown', args[2] or ''))
end)

RegisterNetEvent('chat:addSuggestion')
AddEventHandler('chat:addSuggestion', function(name, help, params)
    -- Handle command suggestions
end)

RegisterNetEvent('chat:removeSuggestion')
AddEventHandler('chat:removeSuggestion', function(name)
    -- Handle removing command suggestions
end)

RegisterNetEvent('chat:clear')
AddEventHandler('chat:clear', function()
    -- Clear chat
end)

-- Basic chat commands
TriggerEvent('chat:addSuggestion', '/say', 'قل شيئاً في الشات العام', {{ name="message", help="الرسالة" }})
TriggerEvent('chat:addSuggestion', '/me', 'أظهر حركة شخصية', {{ name="action", help="الحركة" }})
TriggerEvent('chat:addSuggestion', '/ooc', 'تحدث خارج الشخصية', {{ name="message", help="الرسالة" }})
