# FiveM Server Configuration
# Basic server settings
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"

# Server info
sv_hostname "FiveM Server - Saudi Arabia"
sv_maxclients 64
sv_licensekey "changeme"

# Steam Web API key (optional)
set steam_webApiKey "changeme"

# Database connection (MongoDB Atlas)
set mongodb_connection_string "mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"

# ESX Framework
ensure es_extended
ensure esx_menu_default
ensure esx_menu_dialog
ensure esx_menu_list

# Basic needs
ensure esx_basicneeds
ensure esx_status

# Jobs
ensure esx_jobs
ensure esx_joblisting
ensure esx_policejob
ensure esx_ambulancejob
ensure esx_mechanicjob

# Shops and economy
ensure esx_shops
ensure esx_vehicleshop
ensure esx_banking
ensure esx_atm

# Properties
ensure esx_property
ensure esx_realestateagentjob

# Drugs system
ensure esx_drugs
ensure esx_drugeffects

# Vehicle system
ensure esx_vehiclelock
ensure esx_vehicleshop
ensure esx_carwash

# Inventory and items
ensure esx_inventoryhud
ensure esx_items

# Admin tools
ensure esx_adminmenu

# Map and spawn
ensure spawnmanager
ensure mapmanager
ensure basic-gamemode
ensure hardcap

# Voice chat
ensure pma-voice

# Loading screen
ensure connectqueue

# Anti-cheat
ensure anticheat

# Server commands
add_ace group.admin command allow
add_ace group.admin command.quit deny
add_principal identifier.steam:*************** group.admin

# Game settings
set temp_convar "hey world!"
sets tags "roleplay, esx, saudi, economy"

# OneSync settings
set onesync on

# Server locale
sets locale "ar-SA"
sets tags "roleplay,esx,economy,saudi"

# Restart schedule
set sv_endpointprivacy true
set sv_hostname "^1FiveM Server ^7- ^2Saudi Arabia ^7| ^3Economy RP"

# Load resources
start mapmanager
start spawnmanager
start sessionmanager
start basic-gamemode
start hardcap
start rconlog

# ESX Core
start es_extended

# ESX Menus
start esx_menu_default
start esx_menu_dialog
start esx_menu_list

# ESX Basic
start esx_basicneeds
start esx_status
start esx_voice

# ESX Jobs
start esx_jobs
start esx_joblisting
start esx_policejob
start esx_ambulancejob
start esx_mechanicjob
start esx_realestateagentjob

# ESX Shops
start esx_shops
start esx_vehicleshop
start esx_banking
start esx_atm

# ESX Properties
start esx_property

# ESX Drugs
start esx_drugs
start esx_drugeffects

# ESX Vehicles
start esx_vehiclelock
start esx_carwash

# ESX Inventory
start esx_inventoryhud
start esx_items

# ESX Admin
start esx_adminmenu

# Voice
start pma-voice

# Queue
start connectqueue

# MongoDB
start mongodb

# Additional Settings
set sv_enforceGameBuild 2699
set sv_allowDownload true
set sv_downloadUrl ""

# Anti-cheat settings
set sv_scriptHookAllowed false

# Performance settings
set sv_maxUploadSize 50
set sv_maxDownloadSize 100

# Logging
set sv_logFile "logs/server.log"
set sv_logLevel "info"

# Network settings
set netbandwidth_up 100000
set netbandwidth_down 100000

# Game settings
set gamemode "ESX Roleplay"
set mapname "Los Santos"
set sv_projectName "FiveM Server - Saudi Arabia"
set sv_projectDesc "Roleplay server with ESX Framework and Saudi Riyal currency"

# Discord Rich Presence
set activity_useGameName true
set rich_presence_partySize true
set rich_presence_partyMax true

# Server security
set rcon_password "changeme_rcon_password"
set sv_rconLog true

# Additional convars
set mysql_connection_string "server=localhost;database=essentialmode;userid=root;password="
set es_enableCustomData true

# Restart times (24-hour format)
set sv_scheduledRestartTime "06:00"
set sv_scheduledRestartWarning "10"

# End of configuration
print "^2[Server]^7 FiveM Server - Saudi Arabia Edition loaded!"
print "^2[Server]^7 Currency: Saudi Riyal (SAR)"
print "^2[Server]^7 Database: MongoDB Atlas"
print "^2[Server]^7 Framework: ESX Legacy"
print "^2[Server]^7 Ready for players!"
