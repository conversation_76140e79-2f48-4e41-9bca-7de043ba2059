# FiveM Server Configuration
# Basic server settings
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"

# Server info
sv_hostname "FiveM Server - Saudi Arabia"
sv_maxclients 48
sv_licensekey "cfxk_1o11FlmEpzIMLTHooysYv_46Wk7l"

# Steam Web API key (optional)
set steam_webApiKey "9DD8679C59EEBBED89D419C3F2087380"

# Database connection (MongoDB Atlas)
set mongodb_connection_string "mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"

# ESX Framework (commented out until oxmysql is available)
# ensure es_extended
# ensure esx_basicneeds
# ensure esx_shops
# ensure esx_jobs

# Server commands
add_ace group.admin command allow
add_ace group.admin command.quit deny
add_principal identifier.steam:110000112345678 group.admin

# Game settings
set temp_convar "hey world!"
sets tags "roleplay, esx, saudi, economy"

# OneSync settings
set onesync on

# Server locale
sets locale "ar-SA"
sets tags "roleplay,esx,economy,saudi"

# Restart schedule
set sv_endpointprivacy true
set sv_hostname "^1FiveM Server ^7- ^2Saudi Arabia ^7| ^3Economy RP"

# Essential Resources (Required for FiveM to work)
ensure mapmanager
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap
ensure chat
ensure rconlog
ensure playernames

# Core Resources
ensure mongodb

# Additional Settings
set sv_enforceGameBuild 2699
set sv_allowDownload true
set sv_downloadUrl ""

# Anti-cheat settings
set sv_scriptHookAllowed false

# Performance settings
set sv_maxUploadSize 50
set sv_maxDownloadSize 100

# Logging
set sv_logFile "logs/server.log"
set sv_logLevel "info"

# Network settings
set netbandwidth_up 100000
set netbandwidth_down 100000

# Game settings
set gamemode "ESX Roleplay"
set mapname "Los Santos"
set sv_projectName "FiveM Server - Saudi Arabia"
set sv_projectDesc "Roleplay server with ESX Framework and Saudi Riyal currency"

# Discord Rich Presence
set activity_useGameName true
set rich_presence_partySize true
set rich_presence_partyMax true

# Server security
set rcon_password "changeme_rcon_password"
set sv_rconLog true

# Additional convars
set mysql_connection_string "server=localhost;database=essentialmode;userid=root;password="
set es_enableCustomData true

# Restart times (24-hour format)
set sv_scheduledRestartTime "06:00"
set sv_scheduledRestartWarning "10"

# End of configuration
print "^2[Server]^7 FiveM Server - Saudi Arabia Edition loaded!"
print "^2[Server]^7 Currency: Saudi Riyal (SAR)"
print "^2[Server]^7 Database: MongoDB Atlas"
print "^2[Server]^7 Framework: ESX Legacy"
print "^2[Server]^7 Ready for players!"
